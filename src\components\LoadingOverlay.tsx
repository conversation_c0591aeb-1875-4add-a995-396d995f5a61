import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator, Modal } from 'react-native';
import { Colors, FontSizes, FontWeights, Spacing } from '../constants/theme';

interface LoadingOverlayProps {
  visible: boolean;
  text?: string;
  transparent?: boolean;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  visible,
  text = 'Yükleniyor...',
  transparent = false,
}) => {
  if (!visible) return null;

  const content = (
    <View style={[styles.container, transparent && styles.transparentContainer]}>
      <View style={styles.loadingBox}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>{text}</Text>
      </View>
    </View>
  );

  if (transparent) {
    return content;
  }

  return (
    <Modal transparent visible={visible} animationType="fade">
      {content}
    </Modal>
  );
};

// Simple loading indicator for inline use
export const LoadingIndicator: React.FC<{ size?: 'small' | 'large'; color?: string }> = ({
  size = 'small',
  color = Colors.primary,
}) => (
  <View style={styles.indicatorContainer}>
    <ActivityIndicator size={size} color={color} />
  </View>
);

// Loading state for lists
export const ListLoadingIndicator: React.FC<{ text?: string }> = ({ text = 'Yükleniyor...' }) => (
  <View style={styles.listLoadingContainer}>
    <ActivityIndicator size="small" color={Colors.primary} />
    <Text style={styles.listLoadingText}>{text}</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  transparentContainer: {
    backgroundColor: 'transparent',
  },
  loadingBox: {
    backgroundColor: Colors.white,
    padding: Spacing.xl,
    borderRadius: 16,
    alignItems: 'center',
    minWidth: 120,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  loadingText: {
    fontSize: FontSizes.md,
    fontWeight: FontWeights.medium,
    color: Colors.textPrimary,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },
  indicatorContainer: {
    padding: Spacing.sm,
    alignItems: 'center',
  },
  listLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Spacing.lg,
  },
  listLoadingText: {
    fontSize: FontSizes.sm,
    color: Colors.textSecondary,
    marginLeft: Spacing.sm,
  },
});

export default LoadingOverlay;
