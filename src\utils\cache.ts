import AsyncStorage from '@react-native-async-storage/async-storage';
import { CacheItem } from '../types';

class CacheManager {
  private memoryCache = new Map<string, CacheItem<any>>();

  async get<T>(key: string): Promise<T | null> {
    // Check memory cache first
    const memoryItem = this.memoryCache.get(key);
    if (memoryItem && this.isValid(memoryItem)) {
      return memoryItem.data;
    }

    // Check AsyncStorage
    try {
      const stored = await AsyncStorage.getItem(key);
      if (stored) {
        const item: CacheItem<T> = JSON.parse(stored);
        if (this.isValid(item)) {
          // Update memory cache
          this.memoryCache.set(key, item);
          return item.data;
        } else {
          // Remove expired item
          await AsyncStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.warn('Cache get error:', error);
    }

    return null;
  }

  async set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000): Promise<void> {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
    };

    // Update memory cache
    this.memoryCache.set(key, item);

    // Update AsyncStorage
    try {
      await AsyncStorage.setItem(key, JSON.stringify(item));
    } catch (error) {
      console.warn('Cache set error:', error);
    }
  }

  async clear(): Promise<void> {
    this.memoryCache.clear();
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.warn('Cache clear error:', error);
    }
  }

  private isValid<T>(item: CacheItem<T>): boolean {
    return Date.now() - item.timestamp < item.ttl;
  }
}

export const cacheManager = new CacheManager();
