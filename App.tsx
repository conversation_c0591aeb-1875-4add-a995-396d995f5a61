import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Toast from 'react-native-toast-message';
import { toastConfig } from './src/utils/toast';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { RootStackParamList, MainTabParamList } from './src/types';
import HomeScreen from './src/screens/HomeScreen';
import CategoriesScreen from './src/screens/CategoriesScreen';
import CompaniesScreen from './src/screens/CompaniesScreen';
import AboutScreen from './src/screens/AboutScreen';
import CompanyDetailScreen from './src/screens/CompanyDetailScreen';
import CategoryDetailScreen from './src/screens/CategoryDetailScreen';

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

function MainTabs() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'AnaSayfa':
              iconName = 'home';
              break;
            case 'Kategoriler':
              iconName = 'category';
              break;
            case 'Firmalar':
              iconName = 'business';
              break;
            case 'Hakkinda':
              iconName = 'info';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#2196F3',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: 'white',
          borderTopWidth: 1,
          borderTopColor: '#E0E0E0',
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        headerStyle: {
          backgroundColor: '#2196F3',
        },
        headerTintColor: 'white',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen 
        name="AnaSayfa" 
        component={HomeScreen} 
        options={{ title: 'Ana Sayfa' }}
      />
      <Tab.Screen 
        name="Kategoriler" 
        component={CategoriesScreen} 
        options={{ title: 'Kategoriler' }}
      />
      <Tab.Screen 
        name="Firmalar" 
        component={CompaniesScreen} 
        options={{ title: 'Firmalar' }}
      />
      <Tab.Screen 
        name="Hakkinda" 
        component={AboutScreen} 
        options={{ title: 'Hakkında' }}
      />
    </Tab.Navigator>
  );
}

export default function App() {
  return (
    <>
      <NavigationContainer>
        <Stack.Navigator
          screenOptions={{
            headerStyle: {
              backgroundColor: '#2196F3',
            },
            headerTintColor: 'white',
            headerTitleStyle: {
              fontWeight: 'bold',
            },
          }}
        >
          <Stack.Screen 
            name="MainTabs" 
            component={MainTabs} 
            options={{ headerShown: false }}
          />
          <Stack.Screen 
            name="FirmaDetay" 
            component={CompanyDetailScreen} 
            options={{ title: 'Firma Detayı' }}
          />
          <Stack.Screen 
            name="KategoriDetay" 
            component={CategoryDetailScreen} 
            options={{ title: 'Kategori Detayı' }}
          />
        </Stack.Navigator>
      </NavigationContainer>
      <Toast config={toastConfig} />
      <StatusBar style="light" />
    </>
  );
}
