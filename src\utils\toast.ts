import Toast from 'react-native-toast-message';
import { Colors } from '../constants/theme';

export const showSuccessToast = (title: string, message?: string) => {
  Toast.show({
    type: 'success',
    text1: title,
    text2: message,
    visibilityTime: 3000,
  });
};

export const showErrorToast = (title: string, message?: string) => {
  Toast.show({
    type: 'error',
    text1: title,
    text2: message,
    visibilityTime: 4000,
  });
};

export const showInfoToast = (title: string, message?: string) => {
  Toast.show({
    type: 'info',
    text1: title,
    text2: message,
    visibilityTime: 3000,
  });
};

export const showWarningToast = (title: string, message?: string) => {
  Toast.show({
    type: 'warning',
    text1: title,
    text2: message,
    visibilityTime: 3500,
  });
};

export const hideToast = () => {
  Toast.hide();
};

// Toast configuration for the app
export const toastConfig = {
  success: ({ text1, text2, ...rest }: any) => (
    <Toast
      {...rest}
      style={{
        backgroundColor: Colors.success,
        borderLeftColor: Colors.successDark,
      }}
      text1Style={{
        color: Colors.white,
        fontWeight: 'bold',
      }}
      text2Style={{
        color: Colors.white,
      }}
    />
  ),
  error: ({ text1, text2, ...rest }: any) => (
    <Toast
      {...rest}
      style={{
        backgroundColor: Colors.error,
        borderLeftColor: Colors.errorDark,
      }}
      text1Style={{
        color: Colors.white,
        fontWeight: 'bold',
      }}
      text2Style={{
        color: Colors.white,
      }}
    />
  ),
  info: ({ text1, text2, ...rest }: any) => (
    <Toast
      {...rest}
      style={{
        backgroundColor: Colors.primary,
        borderLeftColor: Colors.primaryDark,
      }}
      text1Style={{
        color: Colors.white,
        fontWeight: 'bold',
      }}
      text2Style={{
        color: Colors.white,
      }}
    />
  ),
  warning: ({ text1, text2, ...rest }: any) => (
    <Toast
      {...rest}
      style={{
        backgroundColor: Colors.warning,
        borderLeftColor: Colors.warningDark,
      }}
      text1Style={{
        color: Colors.white,
        fontWeight: 'bold',
      }}
      text2Style={{
        color: Colors.white,
      }}
    />
  ),
};
