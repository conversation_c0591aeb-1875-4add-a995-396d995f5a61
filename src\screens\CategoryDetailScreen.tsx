import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { RouteProp, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { RootStackParamList, Urun, Firma } from '../types';
import { useApi } from '../hooks/useApi';
import SearchBar from '../components/SearchBar';

type CategoryDetailScreenRouteProp = RouteProp<RootStackParamList, 'KategoriDetay'>;
type CategoryDetailScreenNavigationProp = StackNavigationProp<RootStackParamList>;

interface Props {
  route: CategoryDetailScreenRouteProp;
}

const CategoryDetailScreen: React.FC<Props> = ({ route }) => {
  const { kategori } = route.params;
  const navigation = useNavigation<CategoryDetailScreenNavigationProp>();
  const { urunler, firmalar, loading, refreshData } = useApi();
  const [searchQuery, setSearchQuery] = useState('');

  const categoryProducts = useMemo(() => {
    let filtered = urunler.filter(urun => urun.kategoriId === kategori.id);

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(urun =>
        urun.ad.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [urunler, kategori.id, searchQuery]);

  const getFirma = (firmaId: number): Firma | undefined => {
    return firmalar.find(firma => firma.id === firmaId);
  };

  const handleProductPress = (urun: Urun) => {
    const firma = getFirma(urun.firmaId);
    if (firma) {
      navigation.navigate('FirmaDetay', { firma });
    }
  };

  const renderProductItem = ({ item }: { item: Urun }) => {
    const firma = getFirma(item.firmaId);
    const isBoycotted = firma && !firma.aktif;

    return (
      <TouchableOpacity
        style={styles.productCard}
        onPress={() => handleProductPress(item)}
      >
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{item.ad}</Text>
          <Text style={styles.companyName}>
            {firma?.ad || 'Bilinmeyen Firma'}
          </Text>
          <Text style={styles.companyCountry}>
            {firma?.ulke || 'Bilinmeyen Ülke'}
          </Text>
        </View>

        <View style={styles.productActions}>
          {isBoycotted && (
            <View style={styles.boycottBadge}>
              <Icon name="warning" size={16} color="white" />
              <Text style={styles.boycottText}>Boykotlu</Text>
            </View>
          )}

          <View style={[
            styles.statusBadge,
            { backgroundColor: firma?.aktif ? '#4CAF50' : '#F44336' }
          ]}>
            <Text style={styles.statusText}>
              {firma?.aktif ? 'Aktif' : 'Pasif'}
            </Text>
          </View>

          <Icon name="chevron-right" size={24} color="#666" style={styles.chevron} />
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="inventory" size={64} color="#ccc" />
      <Text style={styles.emptyStateTitle}>Ürün Bulunamadı</Text>
      <Text style={styles.emptyStateText}>
        {searchQuery.trim()
          ? 'Aradığınız kriterlere uygun ürün bulunamadı.'
          : 'Bu kategoride henüz ürün bulunmuyor.'
        }
      </Text>
    </View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Category Info */}
      <View style={styles.categoryInfo}>
        <View style={styles.categoryIcon}>
          <Icon name="category" size={40} color="#2196F3" />
        </View>
        <Text style={styles.categoryName}>{kategori.ad}</Text>
        <Text style={styles.categoryDescription}>{kategori.aciklama}</Text>
      </View>

      {/* Search Bar */}
      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Ürün ara..."
      />

      {/* Stats */}
      <View style={styles.statsContainer}>
        <Text style={styles.statsText}>
          {categoryProducts.length} ürün bulundu
        </Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={categoryProducts}
        renderItem={renderProductItem}
        keyExtractor={(item) => item.id.toString()}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={refreshData} />
        }
        contentContainerStyle={categoryProducts.length === 0 ? styles.emptyContainer : undefined}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    paddingBottom: 16,
  },
  categoryInfo: {
    backgroundColor: 'white',
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  categoryIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  categoryName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  categoryDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  statsContainer: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginTop: 8,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  statsText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  productCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginVertical: 4,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  companyName: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  companyCountry: {
    fontSize: 12,
    color: '#999',
  },
  productActions: {
    alignItems: 'flex-end',
  },
  boycottBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF5722',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 4,
  },
  boycottText: {
    fontSize: 10,
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 10,
    color: 'white',
    fontWeight: 'bold',
  },
  chevron: {
    marginTop: 4,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default CategoryDetailScreen;
