import { useState, useEffect, useCallback } from 'react';
import { apiService } from '../services/api';
import { Firma, Kategori, Urun, Haber, Statistics } from '../types';
import Toast from 'react-native-toast-message';

interface ApiState {
  firmalar: Firma[];
  kategoriler: <PERSON><PERSON><PERSON>[];
  urunler: Urun[];
  haberler: Haber[];
  loading: boolean;
  error: string | null;
  statistics: Statistics;
}

export const useApi = () => {
  const [state, setState] = useState<ApiState>({
    firmalar: [],
    kategoriler: [],
    urunler: [],
    haberler: [],
    loading: true,
    error: null,
    statistics: {
      toplamUrun: 0,
      toplamHaber: 0,
      toplamKategori: 0,
      toplamFirma: 0,
      aktifFirma: 0,
      pasifFirma: 0,
    },
  });

  const calculateStatistics = useCallback((
    firmalar: Firma[],
    kategoriler: <PERSON><PERSON><PERSON>[],
    urunler: <PERSON>run[],
    haberler: Haber[]
  ): Statistics => {
    const aktifFirma = firmalar.filter(f => f.aktif).length;
    const pasifFirma = firmalar.filter(f => !f.aktif).length;

    return {
      toplamUrun: urunler.length,
      toplamHaber: haberler.length,
      toplamKategori: kategoriler.length,
      toplamFirma: firmalar.length,
      aktifFirma,
      pasifFirma,
    };
  }, []);

  const fetchData = useCallback(async (useCache: boolean = true) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const data = await apiService.getAllData(useCache);
      const statistics = calculateStatistics(
        data.firmalar,
        data.kategoriler,
        data.urunler,
        data.haberler
      );

      setState({
        ...data,
        statistics,
        loading: false,
        error: null,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Veri yüklenirken hata oluştu';
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      
      Toast.show({
        type: 'error',
        text1: 'Hata',
        text2: errorMessage,
      });
    }
  }, [calculateStatistics]);

  const refreshData = useCallback(() => {
    fetchData(false);
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    refreshData,
    fetchData,
  };
};
