import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { RootStackParamList, MainTabParamList, UrunFilter, Firma } from '../types';
import { useApi } from '../hooks/useApi';
import { filterUrunler } from '../utils/filters';
import SearchBar from '../components/SearchBar';
import StatCard from '../components/StatCard';
import CompanyCard from '../components/CompanyCard';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList> &
  BottomTabNavigationProp<MainTabParamList>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { firmalar, kategoriler, urunler, haberler, statistics, loading, refreshData } = useApi();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<UrunFilter>('tumu');
  const [selectedKategoriId, setSelectedKategoriId] = useState<number | undefined>();

  const filteredUrunler = useMemo(() => {
    return filterUrunler(urunler, firmalar, selectedFilter, selectedKategoriId, searchQuery);
  }, [urunler, firmalar, selectedFilter, selectedKategoriId, searchQuery]);

  const handleCompanyPress = (firma: Firma) => {
    navigation.navigate('FirmaDetay', { firma });
  };

  const handleFilterPress = (filter: UrunFilter) => {
    if (filter === 'kategori') {
      // Show category selection
      Alert.alert(
        'Kategori Seç',
        'Hangi kategoriyi görmek istiyorsunuz?',
        [
          { text: 'İptal', style: 'cancel' },
          ...kategoriler.map(kategori => ({
            text: kategori.ad,
            onPress: () => {
              setSelectedFilter('kategori');
              setSelectedKategoriId(kategori.id);
            },
          })),
        ]
      );
    } else {
      setSelectedFilter(filter);
      setSelectedKategoriId(undefined);
    }
  };

  const renderCompanyItem = ({ item }: { item: Firma }) => (
    <CompanyCard firma={item} onPress={handleCompanyPress} />
  );

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={refreshData} />
      }
    >
      {/* Search Bar */}
      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Ürün ara..."
      />

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <Text style={styles.sectionTitle}>Filtreler</Text>
        <View style={styles.filterButtons}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedFilter === 'tumu' && styles.activeFilterButton,
            ]}
            onPress={() => handleFilterPress('tumu')}
          >
            <Text
              style={[
                styles.filterButtonText,
                selectedFilter === 'tumu' && styles.activeFilterButtonText,
              ]}
            >
              Tümü
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedFilter === 'boykotlu' && styles.activeFilterButton,
            ]}
            onPress={() => handleFilterPress('boykotlu')}
          >
            <Text
              style={[
                styles.filterButtonText,
                selectedFilter === 'boykotlu' && styles.activeFilterButtonText,
              ]}
            >
              Sadece Boykotlu
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedFilter === 'kategori' && styles.activeFilterButton,
            ]}
            onPress={() => handleFilterPress('kategori')}
          >
            <Text
              style={[
                styles.filterButtonText,
                selectedFilter === 'kategori' && styles.activeFilterButtonText,
              ]}
            >
              Kategoriye Göre
            </Text>
          </TouchableOpacity>
        </View>

        {selectedFilter === 'kategori' && selectedKategoriId && (
          <View style={styles.selectedCategoryContainer}>
            <Text style={styles.selectedCategoryText}>
              Seçili: {kategoriler.find(k => k.id === selectedKategoriId)?.ad}
            </Text>
            <TouchableOpacity
              onPress={() => {
                setSelectedFilter('tumu');
                setSelectedKategoriId(undefined);
              }}
            >
              <Icon name="close" size={20} color="#666" />
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Statistics Cards */}
      <View style={styles.statisticsContainer}>
        <Text style={styles.sectionTitle}>İstatistikler</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <StatCard
            title="Toplam Ürün"
            value={statistics.toplamUrun}
            icon="inventory"
            color="#2196F3"
          />
          <StatCard
            title="Toplam Haber"
            value={statistics.toplamHaber}
            icon="article"
            color="#FF9800"
          />
          <StatCard
            title="Toplam Kategori"
            value={statistics.toplamKategori}
            icon="category"
            color="#9C27B0"
          />
          <StatCard
            title="Toplam Firma"
            value={statistics.toplamFirma}
            icon="business"
            color="#4CAF50"
            subtitle={`Aktif: ${statistics.aktifFirma} | Pasif: ${statistics.pasifFirma}`}
          />
        </ScrollView>
      </View>

      {/* Companies Carousel */}
      <View style={styles.companiesContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Firmalar</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Firmalar' as any)}>
            <Text style={styles.seeAllText}>Tümünü Gör</Text>
          </TouchableOpacity>
        </View>

        <FlatList
          data={firmalar.slice(0, 10)}
          renderItem={renderCompanyItem}
          keyExtractor={(item) => item.id.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.companiesList}
        />
      </View>

      {/* Filtered Products Count */}
      <View style={styles.resultsContainer}>
        <Text style={styles.resultsText}>
          {filteredUrunler.length} ürün bulundu
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  filtersContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  filterButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  activeFilterButton: {
    backgroundColor: '#2196F3',
    borderColor: '#2196F3',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
  },
  activeFilterButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  selectedCategoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#E3F2FD',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  selectedCategoryText: {
    fontSize: 14,
    color: '#1976D2',
    fontWeight: '500',
  },
  statisticsContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  companiesContainer: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  seeAllText: {
    fontSize: 14,
    color: '#2196F3',
    fontWeight: '500',
  },
  companiesList: {
    paddingHorizontal: 8,
  },
  resultsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  resultsText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
});

export default HomeScreen;
