import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Filter } from '../types';

export const filterUrunler = (
  urunler: Urun[],
  firmalar: Firma[],
  filter: UrunFilter,
  kategoriId?: number,
  searchQuery?: string
): Urun[] => {
  let filtered = [...urunler];

  // Apply search filter
  if (searchQuery && searchQuery.trim()) {
    const query = searchQuery.toLowerCase().trim();
    filtered = filtered.filter(urun => 
      urun.ad.toLowerCase().includes(query)
    );
  }

  // Apply category filter
  if (filter === 'kategori' && kategoriId) {
    filtered = filtered.filter(urun => urun.kategoriId === kategoriId);
  }

  // Apply boycott filter
  if (filter === 'boykotlu') {
    const pasifFirmaIds = new Set(
      firmalar.filter(firma => !firma.aktif).map(firma => firma.id)
    );
    filtered = filtered.filter(urun => pasifFirmaIds.has(urun.firmaId));
  }

  return filtered;
};

export const getBoykotluUrunler = (urunler: Urun[], firmalar: Firma[]): Urun[] => {
  const pasifFirmaIds = new Set(
    firmalar.filter(firma => !firma.aktif).map(firma => firma.id)
  );
  return urunler.filter(urun => pasifFirmaIds.has(urun.firmaId));
};

export const searchFirmalar = (firmalar: Firma[], query: string): Firma[] => {
  if (!query.trim()) return firmalar;
  
  const searchTerm = query.toLowerCase().trim();
  return firmalar.filter(firma => 
    firma.ad.toLowerCase().includes(searchTerm) ||
    firma.ulke.toLowerCase().includes(searchTerm)
  );
};

export const filterFirmalar = (
  firmalar: Firma[],
  filter: 'tumu' | 'aktif' | 'pasif'
): Firma[] => {
  switch (filter) {
    case 'aktif':
      return firmalar.filter(firma => firma.aktif);
    case 'pasif':
      return firmalar.filter(firma => !firma.aktif);
    default:
      return firmalar;
  }
};
