import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { RootStackParamList, Firma } from '../types';
import { useApi } from '../hooks/useApi';
import { searchFirmalar, filterFirmalar } from '../utils/filters';
import SearchBar from '../components/SearchBar';

type CompaniesScreenNavigationProp = StackNavigationProp<RootStackParamList>;

type FirmaFilter = 'tumu' | 'aktif' | 'pasif';

const CompaniesScreen: React.FC = () => {
  const navigation = useNavigation<CompaniesScreenNavigationProp>();
  const { firmalar, urunler, loading, refreshData } = useApi();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<FirmaFilter>('tumu');

  const filteredFirmalar = useMemo(() => {
    let filtered = searchFirmalar(firmalar, searchQuery);
    filtered = filterFirmalar(filtered, selectedFilter);
    return filtered;
  }, [firmalar, searchQuery, selectedFilter]);

  const getProductCount = (firmaId: number): number => {
    return urunler.filter(urun => urun.firmaId === firmaId).length;
  };

  const handleCompanyPress = (firma: Firma) => {
    navigation.navigate('FirmaDetay', { firma });
  };

  const handleFilterPress = (filter: FirmaFilter) => {
    setSelectedFilter(filter);
  };

  const renderCompanyItem = ({ item }: { item: Firma }) => {
    const productCount = getProductCount(item.id);

    return (
      <TouchableOpacity
        style={styles.companyCard}
        onPress={() => handleCompanyPress(item)}
      >
        <View style={styles.logoContainer}>
          {item.logoUrl ? (
            <Image source={{ uri: item.logoUrl }} style={styles.logo} />
          ) : (
            <View style={styles.placeholderLogo}>
              <Icon name="business" size={24} color="#666" />
            </View>
          )}
        </View>

        <View style={styles.companyInfo}>
          <Text style={styles.companyName}>{item.ad}</Text>
          <Text style={styles.companyCountry}>{item.ulke}</Text>
          <Text style={styles.productCount}>
            {productCount} ürün
          </Text>
        </View>

        <View style={styles.companyActions}>
          <View style={[
            styles.statusBadge,
            { backgroundColor: item.aktif ? '#4CAF50' : '#F44336' }
          ]}>
            <Icon
              name={item.aktif ? 'check-circle' : 'cancel'}
              size={12}
              color="white"
              style={styles.statusIcon}
            />
            <Text style={styles.statusText}>
              {item.aktif ? 'Aktif' : 'Pasif'}
            </Text>
          </View>

          <Icon name="chevron-right" size={24} color="#666" style={styles.chevron} />
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="search-off" size={64} color="#ccc" />
      <Text style={styles.emptyStateTitle}>Firma Bulunamadı</Text>
      <Text style={styles.emptyStateText}>
        Aradığınız kriterlere uygun firma bulunamadı.
      </Text>
    </View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Firma ara..."
      />

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <Text style={styles.filtersTitle}>Filtreler</Text>
        <View style={styles.filterButtons}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedFilter === 'tumu' && styles.activeFilterButton,
            ]}
            onPress={() => handleFilterPress('tumu')}
          >
            <Text
              style={[
                styles.filterButtonText,
                selectedFilter === 'tumu' && styles.activeFilterButtonText,
              ]}
            >
              Tümü
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedFilter === 'aktif' && styles.activeFilterButton,
            ]}
            onPress={() => handleFilterPress('aktif')}
          >
            <Text
              style={[
                styles.filterButtonText,
                selectedFilter === 'aktif' && styles.activeFilterButtonText,
              ]}
            >
              Sadece Aktif
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedFilter === 'pasif' && styles.activeFilterButton,
            ]}
            onPress={() => handleFilterPress('pasif')}
          >
            <Text
              style={[
                styles.filterButtonText,
                selectedFilter === 'pasif' && styles.activeFilterButtonText,
              ]}
            >
              Sadece Pasif
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Stats */}
      <View style={styles.statsContainer}>
        <Text style={styles.statsText}>
          {filteredFirmalar.length} firma bulundu
        </Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={filteredFirmalar}
        renderItem={renderCompanyItem}
        keyExtractor={(item) => item.id.toString()}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={refreshData} />
        }
        contentContainerStyle={filteredFirmalar.length === 0 ? styles.emptyContainer : undefined}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    paddingBottom: 16,
  },
  filtersContainer: {
    paddingHorizontal: 16,
    marginTop: 8,
  },
  filtersTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  filterButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  activeFilterButton: {
    backgroundColor: '#2196F3',
    borderColor: '#2196F3',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#666',
  },
  activeFilterButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  statsContainer: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginTop: 8,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  statsText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  companyCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginVertical: 4,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoContainer: {
    marginRight: 16,
  },
  logo: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f0f0',
  },
  placeholderLogo: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  companyInfo: {
    flex: 1,
  },
  companyName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  companyCountry: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  productCount: {
    fontSize: 12,
    color: '#2196F3',
    fontWeight: '500',
  },
  companyActions: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 4,
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    fontSize: 10,
    color: 'white',
    fontWeight: 'bold',
  },
  chevron: {
    marginTop: 4,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default CompaniesScreen;
