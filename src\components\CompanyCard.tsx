import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Firma } from '../types';
import { getCardAccessibilityLabel, getStatusAccessibilityLabel } from '../utils/accessibility';

interface CompanyCardProps {
  firma: Firma;
  onPress: (firma: Firma) => void;
}

const CompanyCard: React.FC<CompanyCardProps> = ({ firma, onPress }) => {
  const accessibilityLabel = getCardAccessibilityLabel(
    firma.ad,
    `${firma.ulke}, ${getStatusAccessibilityLabel(firma.aktif, 'Aktif firma', 'Pasif firma, boykotlu')}`
  );

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => onPress(firma)}
      accessible={true}
      accessibilityLabel={accessibilityLabel}
      accessibilityRole="button"
      accessibilityHint="Firma detaylarını görünt<PERSON>lemek için dokunun"
    >
      <View style={styles.logoContainer}>
        {firma.logoUrl ? (
          <Image
            source={{ uri: firma.logoUrl }}
            style={styles.logo}
            accessible={true}
            accessibilityLabel={`${firma.ad} logosu`}
          />
        ) : (
          <View style={styles.placeholderLogo}>
            <Icon name="business" size={32} color="#666" />
          </View>
        )}
      </View>

      <View style={styles.content}>
        <Text style={styles.name} numberOfLines={2}>
          {firma.ad}
        </Text>
        <Text style={styles.country}>{firma.ulke}</Text>

        <View style={[
          styles.statusBadge,
          { backgroundColor: firma.aktif ? '#4CAF50' : '#F44336' }
        ]}>
          <Text style={styles.statusText}>
            {firma.aktif ? 'Aktif' : 'Pasif'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 8,
    marginVertical: 4,
    width: 160,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 12,
  },
  logo: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
  },
  placeholderLogo: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
  },
  name: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 4,
  },
  country: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: 'bold',
  },
});

export default CompanyCard;
