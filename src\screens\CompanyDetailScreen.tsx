import React, { useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  FlatList,
  RefreshControl,
  Image,
  TouchableOpacity,
} from 'react-native';
import { RouteProp } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { RootStackParamList, Urun } from '../types';
import { useApi } from '../hooks/useApi';

type CompanyDetailScreenRouteProp = RouteProp<RootStackParamList, 'FirmaDetay'>;

interface Props {
  route: CompanyDetailScreenRouteProp;
}

const CompanyDetailScreen: React.FC<Props> = ({ route }) => {
  const { firma } = route.params;
  const { urunler, kategoriler, loading, refreshData } = useApi();

  const companyProducts = useMemo(() => {
    return urunler.filter(urun => urun.firmaId === firma.id);
  }, [urunler, firma.id]);

  const getKategoriAdi = (kategoriId: number): string => {
    const kategori = kategoriler.find(k => k.id === kategoriId);
    return kategori?.ad || 'Bilinmeyen Kategori';
  };

  const renderProductItem = ({ item }: { item: Urun }) => (
    <View style={styles.productCard}>
      <View style={styles.productInfo}>
        <Text style={styles.productName}>{item.ad}</Text>
        <Text style={styles.productCategory}>{getKategoriAdi(item.kategoriId)}</Text>
      </View>
      <Icon name="chevron-right" size={24} color="#666" />
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="inventory" size={64} color="#ccc" />
      <Text style={styles.emptyStateTitle}>Ürün Bulunamadı</Text>
      <Text style={styles.emptyStateText}>
        Bu firmaya ait henüz ürün bulunmuyor.
      </Text>
    </View>
  );

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={refreshData} />
      }
    >
      {/* Company Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          {firma.logoUrl ? (
            <Image source={{ uri: firma.logoUrl }} style={styles.logo} />
          ) : (
            <View style={styles.placeholderLogo}>
              <Icon name="business" size={48} color="#666" />
            </View>
          )}
        </View>

        <View style={styles.companyInfo}>
          <Text style={styles.companyName}>{firma.ad}</Text>
          <Text style={styles.companyCountry}>{firma.ulke}</Text>

          <View style={[
            styles.statusBadge,
            { backgroundColor: firma.aktif ? '#4CAF50' : '#F44336' }
          ]}>
            <Icon
              name={firma.aktif ? 'check-circle' : 'cancel'}
              size={16}
              color="white"
              style={styles.statusIcon}
            />
            <Text style={styles.statusText}>
              {firma.aktif ? 'Aktif Firma' : 'Pasif Firma (Boykotlu)'}
            </Text>
          </View>
        </View>
      </View>

      {/* Products Section */}
      <View style={styles.productsSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Ürünler</Text>
          <Text style={styles.productCount}>
            {companyProducts.length} ürün
          </Text>
        </View>

        {companyProducts.length > 0 ? (
          <FlatList
            data={companyProducts}
            renderItem={renderProductItem}
            keyExtractor={(item) => item.id.toString()}
            scrollEnabled={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        ) : (
          renderEmptyState()
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: 'white',
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  logoContainer: {
    marginBottom: 16,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f0f0f0',
  },
  placeholderLogo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  companyInfo: {
    alignItems: 'center',
  },
  companyName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 4,
  },
  companyCountry: {
    fontSize: 16,
    color: '#666',
    marginBottom: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  statusIcon: {
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    color: 'white',
    fontWeight: 'bold',
  },
  productsSection: {
    backgroundColor: 'white',
    margin: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  productCount: {
    fontSize: 14,
    color: '#666',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  productCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  productCategory: {
    fontSize: 14,
    color: '#666',
  },
  separator: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default CompanyDetailScreen;
