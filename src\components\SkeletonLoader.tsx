import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { Colors, BorderRadius } from '../constants/theme';

interface SkeletonLoaderProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: any;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  width = '100%',
  height = 20,
  borderRadius = BorderRadius.md,
  style,
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );

    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [Colors.gray200, Colors.gray300],
  });

  return (
    <Animated.View
      style={[
        styles.skeleton,
        {
          width,
          height,
          borderRadius,
          backgroundColor,
        },
        style,
      ]}
    />
  );
};

// Predefined skeleton components
export const SkeletonCard: React.FC = () => (
  <View style={styles.skeletonCard}>
    <SkeletonLoader width={60} height={60} borderRadius={30} style={styles.skeletonAvatar} />
    <View style={styles.skeletonContent}>
      <SkeletonLoader width="80%" height={16} style={styles.skeletonTitle} />
      <SkeletonLoader width="60%" height={14} style={styles.skeletonSubtitle} />
      <SkeletonLoader width="40%" height={12} />
    </View>
  </View>
);

export const SkeletonList: React.FC<{ count?: number }> = ({ count = 5 }) => (
  <View>
    {Array.from({ length: count }).map((_, index) => (
      <SkeletonCard key={index} />
    ))}
  </View>
);

export const SkeletonStatCard: React.FC = () => (
  <View style={styles.skeletonStatCard}>
    <SkeletonLoader width={48} height={48} borderRadius={24} style={styles.skeletonIcon} />
    <View style={styles.skeletonStatContent}>
      <SkeletonLoader width={60} height={20} style={styles.skeletonStatValue} />
      <SkeletonLoader width={80} height={14} />
    </View>
  </View>
);

const styles = StyleSheet.create({
  skeleton: {
    backgroundColor: Colors.gray200,
  },
  skeletonCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    marginHorizontal: 16,
    marginVertical: 4,
    padding: 16,
    borderRadius: BorderRadius.lg,
  },
  skeletonAvatar: {
    marginRight: 16,
  },
  skeletonContent: {
    flex: 1,
  },
  skeletonTitle: {
    marginBottom: 8,
  },
  skeletonSubtitle: {
    marginBottom: 8,
  },
  skeletonStatCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    padding: 16,
    borderRadius: BorderRadius.xl,
    marginHorizontal: 8,
    marginVertical: 4,
    minWidth: 160,
  },
  skeletonIcon: {
    marginRight: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  skeletonStatContent: {
    flex: 1,
  },
  skeletonStatValue: {
    marginBottom: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
});

export default SkeletonLoader;
