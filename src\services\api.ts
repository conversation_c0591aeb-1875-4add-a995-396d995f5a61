import axios from 'axios';
import { API_BASE_URL, API_ENDPOINTS, CACHE_TTL } from '../constants/api';
import { Firma, Kategori, Urun, Haber } from '../types';
import { cacheManager } from '../utils/cache';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.message);
    return Promise.reject(error);
  }
);

export class ApiService {
  async getFirmalar(useCache: boolean = true): Promise<Firma[]> {
    const cacheKey = 'firmalar';
    
    if (useCache) {
      const cached = await cacheManager.get<Firma[]>(cacheKey);
      if (cached) return cached;
    }

    const response = await api.get<Firma[]>(API_ENDPOINTS.FIRMALAR);
    await cacheManager.set(cacheKey, response.data, CACHE_TTL);
    return response.data;
  }

  async getKategoriler(useCache: boolean = true): Promise<Kategori[]> {
    const cacheKey = 'kategoriler';
    
    if (useCache) {
      const cached = await cacheManager.get<Kategori[]>(cacheKey);
      if (cached) return cached;
    }

    const response = await api.get<Kategori[]>(API_ENDPOINTS.KATEGORILER);
    await cacheManager.set(cacheKey, response.data, CACHE_TTL);
    return response.data;
  }

  async getUrunler(useCache: boolean = true): Promise<Urun[]> {
    const cacheKey = 'urunler';
    
    if (useCache) {
      const cached = await cacheManager.get<Urun[]>(cacheKey);
      if (cached) return cached;
    }

    const response = await api.get<Urun[]>(API_ENDPOINTS.URUNLER);
    await cacheManager.set(cacheKey, response.data, CACHE_TTL);
    return response.data;
  }

  async getHaberler(useCache: boolean = true): Promise<Haber[]> {
    const cacheKey = 'haberler';
    
    if (useCache) {
      const cached = await cacheManager.get<Haber[]>(cacheKey);
      if (cached) return cached;
    }

    const response = await api.get<Haber[]>(API_ENDPOINTS.HABERLER);
    await cacheManager.set(cacheKey, response.data, CACHE_TTL);
    return response.data;
  }

  async getAllData(useCache: boolean = true): Promise<{
    firmalar: Firma[];
    kategoriler: Kategori[];
    urunler: Urun[];
    haberler: Haber[];
  }> {
    const [firmalar, kategoriler, urunler, haberler] = await Promise.all([
      this.getFirmalar(useCache),
      this.getKategoriler(useCache),
      this.getUrunler(useCache),
      this.getHaberler(useCache),
    ]);

    return { firmalar, kategoriler, urunler, haberler };
  }

  async clearCache(): Promise<void> {
    await cacheManager.clear();
  }
}

export const apiService = new ApiService();
