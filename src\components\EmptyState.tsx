import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Colors, FontSizes, FontWeights, Spacing } from '../constants/theme';

interface EmptyStateProps {
  icon: string;
  title: string;
  description: string;
  actionText?: string;
  onActionPress?: () => void;
  style?: any;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  actionText,
  onActionPress,
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      <View style={styles.iconContainer}>
        <Icon name={icon} size={64} color={Colors.gray400} />
      </View>
      
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.description}>{description}</Text>
      
      {actionText && onActionPress && (
        <TouchableOpacity style={styles.actionButton} onPress={onActionPress}>
          <Text style={styles.actionText}>{actionText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

// Predefined empty states
export const NoProductsEmptyState: React.FC<{ onRefresh?: () => void }> = ({ onRefresh }) => (
  <EmptyState
    icon="inventory"
    title="Ürün Bulunamadı"
    description="Henüz hiç ürün bulunmuyor. Lütfen daha sonra tekrar deneyin."
    actionText={onRefresh ? "Yenile" : undefined}
    onActionPress={onRefresh}
  />
);

export const NoCompaniesEmptyState: React.FC<{ onRefresh?: () => void }> = ({ onRefresh }) => (
  <EmptyState
    icon="business"
    title="Firma Bulunamadı"
    description="Henüz hiç firma bulunmuyor. Lütfen daha sonra tekrar deneyin."
    actionText={onRefresh ? "Yenile" : undefined}
    onActionPress={onRefresh}
  />
);

export const NoCategoriesEmptyState: React.FC<{ onRefresh?: () => void }> = ({ onRefresh }) => (
  <EmptyState
    icon="category"
    title="Kategori Bulunamadı"
    description="Henüz hiç kategori bulunmuyor. Lütfen daha sonra tekrar deneyin."
    actionText={onRefresh ? "Yenile" : undefined}
    onActionPress={onRefresh}
  />
);

export const NoNewsEmptyState: React.FC<{ onRefresh?: () => void }> = ({ onRefresh }) => (
  <EmptyState
    icon="article"
    title="Haber Bulunamadı"
    description="Henüz hiç haber bulunmuyor. Lütfen daha sonra tekrar deneyin."
    actionText={onRefresh ? "Yenile" : undefined}
    onActionPress={onRefresh}
  />
);

export const SearchEmptyState: React.FC<{ searchQuery: string; onClearSearch?: () => void }> = ({ 
  searchQuery, 
  onClearSearch 
}) => (
  <EmptyState
    icon="search-off"
    title="Sonuç Bulunamadı"
    description={`"${searchQuery}" için arama sonucu bulunamadı. Farklı anahtar kelimeler deneyin.`}
    actionText={onClearSearch ? "Aramayı Temizle" : undefined}
    onActionPress={onClearSearch}
  />
);

export const NetworkErrorEmptyState: React.FC<{ onRetry?: () => void }> = ({ onRetry }) => (
  <EmptyState
    icon="wifi-off"
    title="Bağlantı Hatası"
    description="İnternet bağlantınızı kontrol edin ve tekrar deneyin."
    actionText={onRetry ? "Tekrar Dene" : undefined}
    onActionPress={onRetry}
  />
);

export const ServerErrorEmptyState: React.FC<{ onRetry?: () => void }> = ({ onRetry }) => (
  <EmptyState
    icon="error"
    title="Sunucu Hatası"
    description="Bir hata oluştu. Lütfen daha sonra tekrar deneyin."
    actionText={onRetry ? "Tekrar Dene" : undefined}
    onActionPress={onRetry}
  />
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
    paddingVertical: Spacing.xxl,
  },
  iconContainer: {
    marginBottom: Spacing.lg,
  },
  title: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  description: {
    fontSize: FontSizes.md,
    color: Colors.textHint,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: Spacing.lg,
  },
  actionButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: 20,
  },
  actionText: {
    fontSize: FontSizes.md,
    fontWeight: FontWeights.medium,
    color: Colors.white,
  },
});

export default EmptyState;
