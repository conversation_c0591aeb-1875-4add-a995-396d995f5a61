import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { RootStackParamList, Kategori } from '../types';
import { useApi } from '../hooks/useApi';
import SearchBar from '../components/SearchBar';

type CategoriesScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const CategoriesScreen: React.FC = () => {
  const navigation = useNavigation<CategoriesScreenNavigationProp>();
  const { kategoriler, urunler, loading, refreshData } = useApi();
  const [searchQuery, setSearchQuery] = useState('');

  const filteredKategoriler = useMemo(() => {
    if (!searchQuery.trim()) return kategoriler;

    const query = searchQuery.toLowerCase().trim();
    return kategoriler.filter(kategori =>
      kategori.ad.toLowerCase().includes(query) ||
      kategori.aciklama.toLowerCase().includes(query)
    );
  }, [kategoriler, searchQuery]);

  const getProductCount = (kategoriId: number): number => {
    return urunler.filter(urun => urun.kategoriId === kategoriId).length;
  };

  const handleCategoryPress = (kategori: Kategori) => {
    navigation.navigate('KategoriDetay', { kategori });
  };

  const renderCategoryItem = ({ item }: { item: Kategori }) => {
    const productCount = getProductCount(item.id);

    return (
      <TouchableOpacity
        style={styles.categoryCard}
        onPress={() => handleCategoryPress(item)}
      >
        <View style={styles.categoryIcon}>
          <Icon name="category" size={32} color="#2196F3" />
        </View>

        <View style={styles.categoryInfo}>
          <Text style={styles.categoryName}>{item.ad}</Text>
          <Text style={styles.categoryDescription} numberOfLines={2}>
            {item.aciklama}
          </Text>
          <Text style={styles.productCount}>
            {productCount} ürün
          </Text>
        </View>

        <Icon name="chevron-right" size={24} color="#666" />
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="search-off" size={64} color="#ccc" />
      <Text style={styles.emptyStateTitle}>Kategori Bulunamadı</Text>
      <Text style={styles.emptyStateText}>
        Aradığınız kriterlere uygun kategori bulunamadı.
      </Text>
    </View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Kategori ara..."
      />

      <View style={styles.statsContainer}>
        <Text style={styles.statsText}>
          {filteredKategoriler.length} kategori bulundu
        </Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={filteredKategoriler}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item.id.toString()}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={refreshData} />
        }
        contentContainerStyle={filteredKategoriler.length === 0 ? styles.emptyContainer : undefined}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    paddingBottom: 16,
  },
  statsContainer: {
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginTop: 8,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  statsText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  categoryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginVertical: 4,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
  productCount: {
    fontSize: 12,
    color: '#2196F3',
    fontWeight: '500',
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    alignSelf: 'flex-start',
  },
  emptyContainer: {
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default CategoriesScreen;
