{"name": "boycott-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "1.17.11", "@react-navigation/bottom-tabs": "^6.5.7", "@react-navigation/native": "^6.1.6", "@react-navigation/native-stack": "^6.9.12", "axios": "^1.11.0", "expo": "~48.0.18", "expo-status-bar": "~1.4.4", "react": "18.2.0", "react-native": "0.71.14", "react-native-safe-area-context": "4.5.0", "react-native-screens": "~3.20.0", "react-native-toast-message": "^2.3.3", "react-native-vector-icons": "^10.3.0", "@types/react": "~18.0.27", "typescript": "^4.9.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "^19.1.10", "@types/react-native": "^0.72.8", "typescript": "^5.9.2"}, "private": true}