import React from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Colors, BorderRadius, Spacing, FontSizes, Shadows } from '../constants/theme';

interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChangeText,
  placeholder = 'Ara...',
}) => {
  return (
    <View style={styles.container}>
      <Icon name="search" size={20} color={Colors.textSecondary} style={styles.icon} />
      <TextInput
        style={styles.input}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={Colors.textHint}
        accessible={true}
        accessibilityLabel={`Arama kutusu, ${placeholder}`}
        accessibilityRole="search"
        accessibilityHint="Arama yapmak için metin girin"
      />
      {value.length > 0 && (
        <TouchableOpacity
          onPress={() => onChangeText('')}
          style={styles.clearIcon}
          accessible={true}
          accessibilityLabel="Aramayı temizle"
          accessibilityRole="button"
          accessibilityHint="Arama metnini silmek için dokunun"
        >
          <Icon
            name="clear"
            size={20}
            color={Colors.textSecondary}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderRadius: BorderRadius.lg,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm + 4,
    marginHorizontal: Spacing.md,
    marginVertical: Spacing.sm,
    ...Shadows.small,
  },
  icon: {
    marginRight: Spacing.sm,
  },
  input: {
    flex: 1,
    fontSize: FontSizes.lg,
    color: Colors.textPrimary,
  },
  clearIcon: {
    marginLeft: Spacing.sm,
  },
});

export default SearchBar;
