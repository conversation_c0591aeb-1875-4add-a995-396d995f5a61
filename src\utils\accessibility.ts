import { AccessibilityInfo } from 'react-native';

export const getAccessibilityLabel = (text: string, role?: string): string => {
  if (role) {
    return `${text}, ${role}`;
  }
  return text;
};

export const getButtonAccessibilityLabel = (text: string, disabled?: boolean): string => {
  let label = `${text}, düğme`;
  if (disabled) {
    label += ', devre dışı';
  }
  return label;
};

export const getCardAccessibilityLabel = (title: string, subtitle?: string): string => {
  let label = `${title}, kart`;
  if (subtitle) {
    label += `, ${subtitle}`;
  }
  return label;
};

export const getStatusAccessibilityLabel = (status: boolean, activeText: string, inactiveText: string): string => {
  return status ? activeText : inactiveText;
};

export const announceForAccessibility = (message: string): void => {
  AccessibilityInfo.announceForAccessibility(message);
};

export const isScreenReaderEnabled = async (): Promise<boolean> => {
  try {
    return await AccessibilityInfo.isScreenReaderEnabled();
  } catch (error) {
    console.warn('Error checking screen reader status:', error);
    return false;
  }
};

export const AccessibilityRoles = {
  BUTTON: 'button',
  LINK: 'link',
  TEXT: 'text',
  HEADER: 'header',
  IMAGE: 'image',
  SEARCH: 'search',
  TAB: 'tab',
  TABLIST: 'tablist',
  LIST: 'list',
  LISTITEM: 'listitem',
} as const;
