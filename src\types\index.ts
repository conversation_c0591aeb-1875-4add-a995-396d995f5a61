// API Response Types
export interface Firma {
  id: number;
  ad: string;
  ulke: string;
  aktif: boolean;
  logoUrl: string;
}

export interface Kategori {
  id: number;
  ad: string;
  aciklama: string;
}

export interface Urun {
  id: number;
  ad: string;
  firmaId: number;
  kategoriId: number;
}

export interface Haber {
  id: number;
  baslik: string;
  icerik: string;
  tarih: string;
}

// Navigation Types
export type RootStackParamList = {
  MainTabs: undefined;
  FirmaDetay: { firma: Firma };
  KategoriDetay: { kategori: Kategori };
};

export type MainTabParamList = {
  AnaSayfa: undefined;
  Kategoriler: undefined;
  Firmalar: undefined;
  Hakkinda: undefined;
};

// Filter Types
export type UrunFilter = 'tumu' | 'boykotlu' | 'kategori';

export interface FilterState {
  type: UrunFilter;
  kategoriId?: number;
  searchQuery: string;
}

// Cache Types
export interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

// Statistics Types
export interface Statistics {
  toplamUrun: number;
  toplamHaber: number;
  toplamKategori: number;
  toplamFirma: number;
  aktifFirma: number;
  pasifFirma: number;
}
