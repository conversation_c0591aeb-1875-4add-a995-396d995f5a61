import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const AboutScreen: React.FC = () => {
  const handleLinkPress = async (url: string, title: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Hata', `${title} açılamadı.`);
      }
    } catch (error) {
      Alert.alert('Hata', 'Link açılırken bir hata oluştu.');
    }
  };

  const handleEmailPress = () => {
    handleLinkPress('mailto:<EMAIL>', 'E-posta');
  };

  const handleWebsitePress = () => {
    handleLinkPress('https://www.boykotapp.com', 'Web sitesi');
  };

  const handlePrivacyPress = () => {
    handleLinkPress('https://www.boykotapp.com/privacy', '<PERSON>izlilik Politikas<PERSON>');
  };

  const handleTermsPress = () => {
    handleLinkPress('https://www.boykotapp.com/terms', 'Kullanım Şartları');
  };

  const InfoCard: React.FC<{ icon: string; title: string; description: string; onPress?: () => void }> = ({
    icon,
    title,
    description,
    onPress,
  }) => {
    const CardComponent = onPress ? TouchableOpacity : View;

    return (
      <CardComponent style={styles.infoCard} onPress={onPress}>
        <View style={styles.cardIcon}>
          <Icon name={icon} size={24} color="#2196F3" />
        </View>
        <View style={styles.cardContent}>
          <Text style={styles.cardTitle}>{title}</Text>
          <Text style={styles.cardDescription}>{description}</Text>
        </View>
        {onPress && (
          <Icon name="chevron-right" size={24} color="#666" />
        )}
      </CardComponent>
    );
  };

  return (
    <ScrollView style={styles.container}>
      {/* App Header */}
      <View style={styles.header}>
        <View style={styles.appIcon}>
          <Icon name="block" size={48} color="white" />
        </View>
        <Text style={styles.appName}>Boykot Uygulaması</Text>
        <Text style={styles.appVersion}>Sürüm 1.0.0</Text>
        <Text style={styles.appDescription}>
          Bilinçli tüketim için firma ve ürün bilgileri
        </Text>
      </View>

      {/* App Info */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Uygulama Hakkında</Text>

        <InfoCard
          icon="info"
          title="Amaç"
          description="Bu uygulama, tüketicilerin bilinçli tercihler yapabilmesi için firma ve ürün bilgilerini sunmaktadır."
        />

        <InfoCard
          icon="security"
          title="Veri Güvenliği"
          description="Verileriniz güvenli sunucularımızda saklanır ve üçüncü taraflarla paylaşılmaz."
        />

        <InfoCard
          icon="update"
          title="Güncel Bilgiler"
          description="Firma ve ürün bilgileri düzenli olarak güncellenir ve doğrulanır."
        />
      </View>

      {/* Contact & Links */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>İletişim & Linkler</Text>

        <InfoCard
          icon="email"
          title="E-posta"
          description="<EMAIL>"
          onPress={handleEmailPress}
        />

        <InfoCard
          icon="language"
          title="Web Sitesi"
          description="www.boykotapp.com"
          onPress={handleWebsitePress}
        />

        <InfoCard
          icon="privacy-tip"
          title="Gizlilik Politikası"
          description="Veri kullanım politikamızı inceleyin"
          onPress={handlePrivacyPress}
        />

        <InfoCard
          icon="description"
          title="Kullanım Şartları"
          description="Uygulama kullanım şartlarını okuyun"
          onPress={handleTermsPress}
        />
      </View>

      {/* Technical Info */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Teknik Bilgiler</Text>

        <InfoCard
          icon="code"
          title="Geliştirme"
          description="React Native & TypeScript ile geliştirilmiştir"
        />

        <InfoCard
          icon="storage"
          title="API"
          description="RESTful API ile veri senkronizasyonu"
        />

        <InfoCard
          icon="offline-pin"
          title="Çevrimdışı Destek"
          description="Veriler yerel olarak önbelleğe alınır"
        />
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          © 2024 Boykot Uygulaması
        </Text>
        <Text style={styles.footerSubtext}>
          Tüm hakları saklıdır.
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: 'white',
    padding: 32,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  appIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  appVersion: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  appDescription: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
  section: {
    marginTop: 16,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  cardContent: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  cardDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  footer: {
    alignItems: 'center',
    padding: 32,
    marginTop: 16,
  },
  footerText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  footerSubtext: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
});

export default AboutScreen;
