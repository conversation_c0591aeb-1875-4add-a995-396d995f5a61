# Boykot Uygulaması

React Native (Expo + TypeScript) ile geliştirilmiş modern bir mobil uygulama. Tüketicilerin bilinçli tercihler yapabilmesi için firma ve ürün bilgilerini sunar.

## 🚀 Özellikler

### 📱 Ana Sayfa
- Ürün arama çubuğu
- Akıll<PERSON> filtreler (Tümü, Sadece Boykotlu, Kategoriye Göre)
- Duolingo tarzı renkli istatistik kartları
- Firma carousel'ı
- Pull-to-refresh desteği

### 🏢 Firmalar
- Firma listesi ve arama
- Aktif/Pasif filtreler
- Firma detay sayfaları
- Logo gösterimi
- Durum badge'leri

### 📂 Kategoriler
- Kategori listesi ve arama
- Kategori detay sayfaları
- Ürün sayım bilgileri

### ℹ️ Hakkında
- Uygulama bilgileri
- İletişim linkleri
- Gizlilik politikası
- Kullanım şartları

## 🛠 Teknolojiler

- **React Native** - Mobil uygulama framework'ü
- **Expo** - Geliştirme ve dağıtım platformu
- **TypeScript** - Tip güvenliği
- **React Navigation** - Navigasyon sistemi
- **Axios** - HTTP istekleri
- **AsyncStorage** - Yerel veri depolama
- **Vector Icons** - İkon sistemi
- **Toast Messages** - Bildirim sistemi

## 📋 API Endpoints

Uygulama `http://localhost:5130/` adresindeki API'yi kullanır:

- `GET /api/Firmalar` - Firma listesi
- `GET /api/Kategoriler` - Kategori listesi  
- `GET /api/Urunler` - Ürün listesi
- `GET /api/Haberler` - Haber listesi

### API Veri Yapıları

```typescript
interface Firma {
  id: number;
  ad: string;
  ulke: string;
  aktif: boolean;
  logoUrl: string;
}

interface Kategori {
  id: number;
  ad: string;
  aciklama: string;
}

interface Urun {
  id: number;
  ad: string;
  firmaId: number;
  kategoriId: number;
}

interface Haber {
  id: number;
  baslik: string;
  icerik: string;
  tarih: string;
}
```

## 🚀 Kurulum

1. **Projeyi klonlayın:**
```bash
git clone <repository-url>
cd BoycottApp
```

2. **Bağımlılıkları yükleyin:**
```bash
npm install
```

3. **Uygulamayı başlatın:**
```bash
npm start
```

4. **Expo Go ile test edin:**
   - Android: Expo Go uygulaması ile QR kodu tarayın
   - iOS: Kamera uygulaması ile QR kodu tarayın

## 📱 Navigasyon Yapısı

```
MainTabs (Bottom Tab Navigator)
├── Ana Sayfa
├── Kategoriler
├── Firmalar
└── Hakkında

Stack Navigator
├── MainTabs
├── Firma Detay
└── Kategori Detay
```

## 🎨 UI/UX Özellikleri

- **Modern Tasarım**: Duolingo tarzı renkli ve yuvarlatılmış kartlar
- **Erişilebilirlik**: Screen reader desteği ve erişilebilir label'lar
- **Responsive**: Farklı ekran boyutlarına uyumlu
- **Skeleton Loading**: Yükleme sırasında placeholder'lar
- **Empty States**: Boş durum mesajları ve görselleri
- **Toast Notifications**: Kullanıcı dostu bildirimler

## 🔧 Geliştirme

### Proje Yapısı
```
src/
├── components/     # Yeniden kullanılabilir bileşenler
├── screens/        # Ekran bileşenleri
├── services/       # API servisleri
├── types/          # TypeScript tip tanımları
├── utils/          # Yardımcı fonksiyonlar
├── hooks/          # Custom hook'lar
└── constants/      # Sabitler ve tema
```

### Önemli Dosyalar
- `src/services/api.ts` - API servis katmanı
- `src/utils/cache.ts` - Önbellek yönetimi
- `src/constants/theme.ts` - Tema ve renk sistemi
- `src/hooks/useApi.ts` - API veri yönetimi hook'u

## 📊 Veri Yönetimi

- **Cache Sistemi**: 5 dakika TTL ile bellekte ve AsyncStorage'da önbellek
- **Pull-to-Refresh**: Tüm listelerde yenileme desteği
- **Error Handling**: Kapsamlı hata yönetimi ve kullanıcı bildirimleri
- **Loading States**: Skeleton loader'lar ve loading indicator'ları

## 🔍 Filtreleme ve Arama

- **Ürün Filtreleri**: Tümü, Sadece Boykotlu, Kategoriye Göre
- **Firma Filtreleri**: Tümü, Sadece Aktif, Sadece Pasif
- **Arama**: Ürün adı, firma adı ve kategori bazında arama
- **Real-time**: Anlık filtreleme ve arama sonuçları

## 📈 İstatistikler

Ana sayfada gösterilen istatistikler:
- Toplam Ürün Sayısı
- Toplam Haber Sayısı  
- Toplam Kategori Sayısı
- Toplam Firma Sayısı (Aktif/Pasif ayrımıyla)

## 🌐 Boykot Mantığı

"Boykotlu ürün" tanımı: Firması pasif (aktif=false) olan ürünler boykotlu olarak kabul edilir.

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
